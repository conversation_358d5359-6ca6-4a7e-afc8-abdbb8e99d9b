@import url('widgets.css');

/* FORM ROWS */

.form-row {
    overflow: hidden;
    padding: 10px;
    font-size: 0.8125rem;
    border-bottom: 1px solid var(--hairline-color);
}

.form-row img, .form-row input {
    vertical-align: middle;
}

.form-row label input[type="checkbox"] {
    margin-top: 0;
    vertical-align: 0;
}

form .form-row p {
    padding-left: 0;
}

/* FORM LABELS */

label {
    font-weight: normal;
    color: var(--body-quiet-color);
    font-size: 0.8125rem;
}

.required label, label.required {
    font-weight: bold;
    color: var(--body-fg);
}

/* RADIO BUTTONS */

form div.radiolist div {
    padding-right: 7px;
}

form div.radiolist.inline div {
    display: inline-block;
}

form div.radiolist label {
    width: auto;
}

form div.radiolist input[type="radio"] {
    margin: -2px 4px 0 0;
    padding: 0;
}

form ul.inline {
    margin-left: 0;
    padding: 0;
}

form ul.inline li {
    float: left;
    padding-right: 7px;
}

/* ALIGNED FIELDSETS */

.aligned label {
    display: block;
    padding: 4px 10px 0 0;
    float: left;
    width: 160px;
    word-wrap: break-word;
    line-height: 1;
}

.aligned label:not(.vCheckboxLabel):after {
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 26px;
}

.aligned label + p, .aligned label + div.help, .aligned label + div.readonly {
    padding: 6px 0;
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 170px;
    overflow-wrap: break-word;
}

.aligned ul label {
    display: inline;
    float: none;
    width: auto;
}

.aligned .form-row input {
    margin-bottom: 0;
}

.colMS .aligned .vLargeTextField, .colMS .aligned .vXMLLargeTextField {
    width: 350px;
}

form .aligned ul {
    margin-left: 160px;
    padding-left: 10px;
}

form .aligned div.radiolist {
    display: inline-block;
    margin: 0;
    padding: 0;
}

form .aligned p.help,
form .aligned div.help {
    clear: left;
    margin-top: 0;
    margin-left: 160px;
    padding-left: 10px;
}

form .aligned label + p.help,
form .aligned label + div.help {
    margin-left: 0;
    padding-left: 0;
}

form .aligned p.help:last-child,
form .aligned div.help:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}

form .aligned input + p.help,
form .aligned textarea + p.help,
form .aligned select + p.help,
form .aligned input + div.help,
form .aligned textarea + div.help,
form .aligned select + div.help {
    margin-left: 160px;
    padding-left: 10px;
}

form .aligned ul li {
    list-style: none;
}

form .aligned table p {
    margin-left: 0;
    padding-left: 0;
}

.aligned .vCheckboxLabel {
    float: none;
    width: auto;
    display: inline-block;
    vertical-align: -3px;
    padding: 0 0 5px 5px;
}

.aligned .vCheckboxLabel + p.help,
.aligned .vCheckboxLabel + div.help {
    margin-top: -4px;
}

.colM .aligned .vLargeTextField, .colM .aligned .vXMLLargeTextField {
    width: 610px;
}

.checkbox-row p.help,
.checkbox-row div.help {
    margin-left: 0;
    padding-left: 0;
}

fieldset .fieldBox {
    float: left;
    margin-right: 20px;
}

/* WIDE FIELDSETS */

.wide label {
    width: 200px;
}

form .wide p,
form .wide input + p.help,
form .wide input + div.help {
    margin-left: 200px;
}

form .wide p.help,
form .wide div.help {
    padding-left: 38px;
}

form div.help ul {
    padding-left: 0;
    margin-left: 0;
}

.colM fieldset.wide .vLargeTextField, .colM fieldset.wide .vXMLLargeTextField {
    width: 450px;
}

/* COLLAPSED FIELDSETS */

fieldset.collapsed * {
    display: none;
}

fieldset.collapsed h2, fieldset.collapsed {
    display: block;
}

fieldset.collapsed {
    border: 1px solid var(--hairline-color);
    border-radius: 4px;
    overflow: hidden;
}

fieldset.collapsed h2 {
    background: var(--darkened-bg);
    color: var(--body-quiet-color);
}

fieldset .collapse-toggle {
    color: var(--header-link-color);
}

fieldset.collapsed .collapse-toggle {
    background: transparent;
    display: inline;
    color: var(--link-fg);
}

/* MONOSPACE TEXTAREAS */

fieldset.monospace textarea {
    font-family: "Bitstream Vera Sans Mono", Monaco, "Courier New", Courier, monospace;
}

/* SUBMIT ROW */

.submit-row {
    padding: 12px 14px 7px;
    margin: 0 0 20px;
    background: var(--darkened-bg);
    border: 1px solid var(--hairline-color);
    border-radius: 4px;
    text-align: right;
    overflow: hidden;
}

body.popup .submit-row {
    overflow: auto;
}

.submit-row input {
    height: 35px;
    line-height: 15px;
    margin: 0 0 5px 5px;
}

.submit-row input.default {
    margin: 0 0 5px 8px;
    text-transform: uppercase;
}

.submit-row p {
    margin: 0.3em;
}

.submit-row p.deletelink-box {
    float: left;
    margin: 0;
}

.submit-row a.deletelink {
    display: block;
    background: var(--delete-button-bg);
    border-radius: 4px;
    padding: 10px 15px;
    height: 15px;
    line-height: 15px;
    margin-bottom: 5px;
    color: var(--button-fg);
}

.submit-row a.closelink {
    display: inline-block;
    background: var(--close-button-bg);
    border-radius: 4px;
    padding: 10px 15px;
    height: 15px;
    line-height: 15px;
    margin: 0 0 0 5px;
    color: var(--button-fg);
}

.submit-row a.deletelink:focus,
.submit-row a.deletelink:hover,
.submit-row a.deletelink:active {
    background: var(--delete-button-hover-bg);
}

.submit-row a.closelink:focus,
.submit-row a.closelink:hover,
.submit-row a.closelink:active {
    background: var(--close-button-hover-bg);
}

/* CUSTOM FORM FIELDS */

.vSelectMultipleField {
    vertical-align: top;
}

.vCheckboxField {
    border: none;
}

.vDateField, .vTimeField {
    margin-right: 2px;
    margin-bottom: 4px;
}

.vDateField {
    min-width: 6.85em;
}

.vTimeField {
    min-width: 4.7em;
}

.vURLField {
    width: 30em;
}

.vLargeTextField, .vXMLLargeTextField {
    width: 48em;
}

.flatpages-flatpage #id_content {
    height: 40.2em;
}

.module table .vPositiveSmallIntegerField {
    width: 2.2em;
}

.vIntegerField {
    width: 5em;
}

.vBigIntegerField {
    width: 10em;
}

.vForeignKeyRawIdAdminField {
    width: 5em;
}

.vTextField, .vUUIDField {
    width: 20em;
}

/* INLINES */

.inline-group {
    padding: 0;
    margin: 0 0 30px;
}

.inline-group thead th {
    padding: 8px 10px;
}

.inline-group .aligned label {
    width: 160px;
}

.inline-related {
    position: relative;
}

.inline-related h3 {
    margin: 0;
    color: var(--body-quiet-color);
    padding: 5px;
    font-size: 0.8125rem;
    background: var(--darkened-bg);
    border-top: 1px solid var(--hairline-color);
    border-bottom: 1px solid var(--hairline-color);
}

.inline-related h3 span.delete {
    float: right;
}

.inline-related h3 span.delete label {
    margin-left: 2px;
    font-size: 0.6875rem;
}

.inline-related fieldset {
    margin: 0;
    background: var(--body-bg);
    border: none;
    width: 100%;
}

.inline-related fieldset.module h3 {
    margin: 0;
    padding: 2px 5px 3px 5px;
    font-size: 0.6875rem;
    text-align: left;
    font-weight: bold;
    background: #bcd;
    color: var(--body-bg);
}

.inline-group .tabular fieldset.module {
    border: none;
}

.inline-related.tabular fieldset.module table {
    width: 100%;
    overflow-x: scroll;
}

.last-related fieldset {
    border: none;
}

.inline-group .tabular tr.has_original td {
    padding-top: 2em;
}

.inline-group .tabular tr td.original {
    padding: 2px 0 0 0;
    width: 0;
    _position: relative;
}

.inline-group .tabular th.original {
    width: 0px;
    padding: 0;
}

.inline-group .tabular td.original p {
    position: absolute;
    left: 0;
    height: 1.1em;
    padding: 2px 9px;
    overflow: hidden;
    font-size: 0.5625rem;
    font-weight: bold;
    color: var(--body-quiet-color);
    _width: 700px;
}

.inline-group ul.tools {
    padding: 0;
    margin: 0;
    list-style: none;
}

.inline-group ul.tools li {
    display: inline;
    padding: 0 5px;
}

.inline-group div.add-row,
.inline-group .tabular tr.add-row td {
    color: var(--body-quiet-color);
    background: var(--darkened-bg);
    padding: 8px 10px;
    border-bottom: 1px solid var(--hairline-color);
}

.inline-group .tabular tr.add-row td {
    padding: 8px 10px;
    border-bottom: 1px solid var(--hairline-color);
}

.inline-group ul.tools a.add,
.inline-group div.add-row a,
.inline-group .tabular tr.add-row td a {
    background: url(../img/icon-addlink.svg) 0 1px no-repeat;
    padding-left: 16px;
    font-size: 0.75rem;
}

.empty-form {
    display: none;
}

/* RELATED FIELD ADD ONE / LOOKUP */

.related-lookup {
    margin-left: 5px;
    display: inline-block;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-size: 14px;
}

.related-lookup {
    width: 16px;
    height: 16px;
    background-image: url(../img/search.svg);
}

form .related-widget-wrapper ul {
    display: inline-block;
    margin-left: 0;
    padding-left: 0;
}

.clearable-file-input input {
    margin-top: 0;
}
