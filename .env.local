SECRET_KEY='django-insecure-q$%e4cuap-z42=zp#m1*+b5l=!!v!u=(x^y_0*=dekz7-)5*g1'#"YzxVhevXw4VQB)z[l{X.jRN!Oy>%5kI3Y&46|^V}ov_*qls=x))~xeqZ+#x{L8GW"

# 0 False, 1 True
DEBUG="1"

# Comma Separated values
ALLOWED_HOSTS="127.0.0.1, localhost, *************, api.athlan.com.br"

DJANGO_CSRF_TRUSTED_ORIGINS=http://localhost:8000

DB_ENGINE="django.db.backends.postgresql_psycopg2"
POSTGRES_DB="postgres"
POSTGRES_USER="postgres"
POSTGRES_PASSWORD="Shippou.2003"
POSTGRES_HOST="jp-db.c1y6wwic8wtz.sa-east-1.rds.amazonaws.com"
POSTGRES_PORT="5432"